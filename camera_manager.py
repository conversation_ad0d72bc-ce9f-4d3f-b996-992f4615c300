import cv2
import threading
import time
import os
from datetime import datetime
from ultralytics import YOLO
import numpy as np
from PIL import Image
import torch
from config import Config
from database import db, Detection

class CameraManager:
    def __init__(self):
        self.active_cameras = {}
        self.camera_threads = {}
        self.model = None
        self.load_yolo_model()
    
    def load_yolo_model(self):
        """Load YOLO model"""
        try:
            self.model = YOLO(Config.YOLO_MODEL)
            print(f"YOLO model {Config.YOLO_MODEL} loaded successfully!")
        except Exception as e:
            print(f"Error loading YOLO model: {e}")
            self.model = None
    
    def start_camera_stream(self, camera):
        """Start processing a camera stream"""
        if camera.id in self.active_cameras:
            print(f"Camera {camera.id} is already active")
            return False
        
        # Create thread for this camera
        thread = threading.Thread(
            target=self._process_camera_stream,
            args=(camera,),
            daemon=True
        )
        
        self.camera_threads[camera.id] = thread
        self.active_cameras[camera.id] = True
        thread.start()
        
        print(f"Started processing camera {camera.id}")
        return True
    
    def stop_camera_stream(self, camera_id):
        """Stop processing a camera stream"""
        if camera_id in self.active_cameras:
            self.active_cameras[camera_id] = False
            if camera_id in self.camera_threads:
                # Wait for thread to finish
                self.camera_threads[camera_id].join(timeout=5)
                del self.camera_threads[camera_id]
            print(f"Stopped processing camera {camera_id}")
            return True
        return False
    
    def _process_camera_stream(self, camera):
        """Process video stream from camera"""
        cap = None
        frame_count = 0
        last_detection_time = 0
        
        try:
            # Open video stream
            cap = cv2.VideoCapture(camera.stream_url)
            if not cap.isOpened():
                print(f"Failed to open stream for camera {camera.id}: {camera.stream_url}")
                return
            
            print(f"Successfully opened stream for camera {camera.id}")
            
            while self.active_cameras.get(camera.id, False):
                ret, frame = cap.read()
                if not ret:
                    print(f"Failed to read frame from camera {camera.id}")
                    time.sleep(1)
                    continue
                
                frame_count += 1
                current_time = time.time()
                
                # Skip frames to reduce processing load
                if frame_count % Config.FRAME_SKIP != 0:
                    continue
                
                # Check if enough time has passed since last detection
                if current_time - last_detection_time < Config.DETECTION_INTERVAL:
                    continue
                
                # Perform object detection
                if self.model:
                    self._detect_objects(camera, frame)
                    last_detection_time = current_time
                
                time.sleep(0.1)  # Small delay to prevent excessive CPU usage
                
        except Exception as e:
            print(f"Error processing camera {camera.id}: {e}")
        finally:
            if cap:
                cap.release()
            if camera.id in self.active_cameras:
                del self.active_cameras[camera.id]
    
    def _detect_objects(self, camera, frame):
        """Perform YOLO object detection on frame"""
        try:
            # Run YOLO detection
            results = self.model(frame, conf=Config.CONFIDENCE_THRESHOLD, iou=Config.IOU_THRESHOLD)
            
            if not results or len(results) == 0:
                return
            
            result = results[0]
            
            # Get camera's target object types
            target_objects = camera.get_object_types()
            
            # Extract detection information
            detected_objects = []
            confidence_scores = []
            bounding_boxes = []
            
            if result.boxes is not None:
                for box in result.boxes:
                    # Get class name
                    class_id = int(box.cls[0])
                    class_name = self.model.names[class_id]
                    
                    # Check if this object type is in our target list
                    if class_name in target_objects:
                        confidence = float(box.conf[0])
                        
                        # Get bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].tolist()
                        
                        detected_objects.append(class_name)
                        confidence_scores.append(confidence)
                        bounding_boxes.append([x1, y1, x2, y2])
            
            # If objects were detected, save the detection
            if detected_objects:
                self._save_detection(camera, frame, detected_objects, confidence_scores, bounding_boxes)
                
        except Exception as e:
            print(f"Error in object detection for camera {camera.id}: {e}")
    
    def _save_detection(self, camera, frame, objects, confidences, boxes):
        """Save detection results to database and image to disk"""
        try:
            # Create filename with timestamp
            timestamp = datetime.now()
            filename = f"{camera.id}_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg"
            image_path = os.path.join(Config.DETECTION_IMAGES_DIR, filename)
            
            # Draw bounding boxes on frame
            annotated_frame = frame.copy()
            for i, (obj, conf, box) in enumerate(zip(objects, confidences, boxes)):
                x1, y1, x2, y2 = map(int, box)
                
                # Draw rectangle
                cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Draw label
                label = f"{obj}: {conf:.2f}"
                cv2.putText(annotated_frame, label, (x1, y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # Save annotated image
            cv2.imwrite(image_path, annotated_frame)
            
            # Create detection record
            detection = Detection(
                camera_id=camera.id,
                timestamp=timestamp,
                image_path=image_path
            )
            detection.set_detection_data(objects, confidences, boxes)
            
            # Save to database
            db.session.add(detection)
            
            # Update camera's last detection time
            camera.last_detection = timestamp
            
            db.session.commit()
            
            print(f"Detection saved for camera {camera.id}: {objects}")
            
            # Clean up old images if necessary
            self._cleanup_old_images(camera.id)
            
        except Exception as e:
            print(f"Error saving detection for camera {camera.id}: {e}")
            db.session.rollback()
    
    def _cleanup_old_images(self, camera_id):
        """Remove old detection images if exceeding limit"""
        try:
            # Get all detections for this camera, ordered by timestamp
            detections = Detection.query.filter_by(camera_id=camera_id)\
                                      .order_by(Detection.timestamp.desc())\
                                      .all()
            
            if len(detections) > Config.MAX_IMAGES_PER_CAMERA:
                # Remove excess detections and their images
                excess_detections = detections[Config.MAX_IMAGES_PER_CAMERA:]
                
                for detection in excess_detections:
                    # Remove image file
                    if os.path.exists(detection.image_path):
                        os.remove(detection.image_path)
                    
                    # Remove database record
                    db.session.delete(detection)
                
                db.session.commit()
                print(f"Cleaned up {len(excess_detections)} old detections for camera {camera_id}")
                
        except Exception as e:
            print(f"Error cleaning up old images for camera {camera_id}: {e}")
    
    def get_camera_status(self, camera_id):
        """Get status of a camera"""
        return {
            'camera_id': camera_id,
            'is_active': camera_id in self.active_cameras,
            'is_streaming': self.active_cameras.get(camera_id, False)
        }

# Global camera manager instance
camera_manager = CameraManager()
