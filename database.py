from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class Camera(db.Model):
    __tablename__ = 'cameras'
    
    id = db.Column(db.String(50), primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    stream_type = db.Column(db.String(20), nullable=False)
    stream_url = db.Column(db.String(500), nullable=False)
    object_types = db.Column(db.Text, nullable=False)  # JSON string of object types
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_detection = db.Column(db.DateTime)
    
    # Relationship with detections
    detections = db.relationship('Detection', backref='camera', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'camera_id': self.id,
            'name': self.name,
            'stream_type': self.stream_type,
            'stream_url': self.stream_url,
            'object_types': json.loads(self.object_types),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_detection': self.last_detection.isoformat() if self.last_detection else None,
            'total_detections': len(self.detections)
        }
    
    def set_object_types(self, object_types_list):
        self.object_types = json.dumps(object_types_list)
    
    def get_object_types(self):
        return json.loads(self.object_types)

class Detection(db.Model):
    __tablename__ = 'detections'
    
    id = db.Column(db.Integer, primary_key=True)
    camera_id = db.Column(db.String(50), db.ForeignKey('cameras.id'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    image_path = db.Column(db.String(500), nullable=False)
    objects_detected = db.Column(db.Text, nullable=False)  # JSON string of detected objects
    confidence_scores = db.Column(db.Text)  # JSON string of confidence scores
    bounding_boxes = db.Column(db.Text)  # JSON string of bounding box coordinates
    
    def to_dict(self):
        return {
            'id': self.id,
            'camera_id': self.camera_id,
            'timestamp': self.timestamp.isoformat(),
            'image_path': self.image_path,
            'objects_detected': json.loads(self.objects_detected),
            'confidence_scores': json.loads(self.confidence_scores) if self.confidence_scores else [],
            'bounding_boxes': json.loads(self.bounding_boxes) if self.bounding_boxes else []
        }
    
    def set_detection_data(self, objects, confidences=None, boxes=None):
        self.objects_detected = json.dumps(objects)
        if confidences:
            self.confidence_scores = json.dumps(confidences)
        if boxes:
            self.bounding_boxes = json.dumps(boxes)

def init_db(app):
    """Initialize database with Flask app"""
    db.init_app(app)
    with app.app_context():
        db.create_all()
        print("Database initialized successfully!")
