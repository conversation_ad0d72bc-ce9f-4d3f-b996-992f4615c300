import os

class Config:
    # Database configuration
    DATABASE_URL = 'sqlite:///cameras.db'
    
    # YOLO model configuration
    YOLO_MODEL = 'yolov8n.pt'  # You can use yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt for better accuracy
    
    # Detection settings
    CONFIDENCE_THRESHOLD = 0.3  # Lowered for better detection
    IOU_THRESHOLD = 0.45

    # Image storage
    DETECTION_IMAGES_DIR = 'detection_images'
    MAX_IMAGES_PER_CAMERA = 100

    # Stream processing
    FRAME_SKIP = 2  # Process every 2nd frame for better detection
    DETECTION_INTERVAL = 1  # Reduced to 1 second between detections
    
    # API settings
    HOST = '0.0.0.0'
    PORT = 8080
    DEBUG = True
    
    # Supported stream types
    SUPPORTED_STREAM_TYPES = ['rtsp', 'http', 'https', 'file']
    
    # Default object types for detection (COCO classes)
    DEFAULT_OBJECT_TYPES = [
        'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'licence plate',
        'boat', 'traffic light', 'full trashcan', 'fire', 'smoke', 'mouse',
    ]

# Create directories if they don't exist
os.makedirs(Config.DETECTION_IMAGES_DIR, exist_ok=True)
