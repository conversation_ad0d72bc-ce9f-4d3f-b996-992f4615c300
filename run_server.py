#!/usr/bin/env python3
"""
Startup script for the IP Camera Object Detection REST Service
Handles PyTorch compatibility issues
"""

import sys
import os

def setup_pytorch_compatibility():
    """Setup PyTorch compatibility for YOLO model loading"""
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        
        # Handle PyTorch 2.6+ security requirements
        if hasattr(torch.serialization, 'add_safe_globals'):
            try:
                # Add safe globals for ultralytics
                torch.serialization.add_safe_globals([
                    'ultralytics.nn.tasks.DetectionModel',
                    'ultralytics.nn.modules.block.C2f',
                    'ultralytics.nn.modules.conv.Conv',
                    'ultralytics.nn.modules.head.Detect',
                    'ultralytics.nn.modules.block.SPPF',
                    'collections.OrderedDict'
                ])
                print("Added safe globals for PyTorch 2.6+ compatibility")
            except Exception as e:
                print(f"Warning: Could not add safe globals: {e}")
        
        # Alternative: Set environment variable to allow unsafe loading (for trusted models)
        os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = 'True'
        
    except ImportError:
        print("PyTorch not installed. Please install requirements first.")
        sys.exit(1)

def main():
    """Main startup function"""
    print("Starting IP Camera Object Detection Service...")
    print("=" * 50)
    
    # Setup PyTorch compatibility
    setup_pytorch_compatibility()
    
    # Import and run the Flask app
    try:
        from app import app
        from config import Config
        
        print(f"Server starting on {Config.HOST}:{Config.PORT}")
        print("API Documentation available in README.md")
        print("Sample requests available in sample_requests.json")
        print("-" * 50)
        
        app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
