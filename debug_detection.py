#!/usr/bin/env python3
"""
Debug script for detection issues
"""

import requests
import json
import os
import cv2
from ultralytics import YOLO
import torch

BASE_URL = "http://127.0.0.1:8080"

def check_yolo_model():
    """Check if YOLO model is working"""
    print("🔍 Checking YOLO Model...")
    try:
        model = YOLO('yolov8n.pt')
        print("✅ YOLO model loaded successfully")
        
        # Test with a sample image
        import numpy as np
        test_image = np.zeros((640, 640, 3), dtype=np.uint8)
        results = model(test_image)
        print("✅ YOLO inference test passed")
        return True
    except Exception as e:
        print(f"❌ YOLO model error: {e}")
        return False

def check_camera_status():
    """Check camera status via API"""
    print("\n📹 Checking Camera Status...")
    try:
        response = requests.get(f"{BASE_URL}/cameras")
        if response.status_code == 200:
            cameras = response.json()
            print(f"✅ Found {cameras['total']} cameras")
            
            for camera in cameras['cameras']:
                camera_id = camera['camera_id']
                print(f"\n📷 Camera: {camera['name']} ({camera_id})")
                print(f"   Stream URL: {camera['stream_url']}")
                print(f"   Stream Type: {camera['stream_type']}")
                print(f"   Is Active: {camera.get('is_active', 'Unknown')}")
                print(f"   Is Streaming: {camera.get('is_streaming', 'Unknown')}")
                print(f"   Total Detections: {camera.get('total_detections', 0)}")
                print(f"   Object Types: {camera.get('object_types', [])}")
                
                # Check detailed status
                status_response = requests.get(f"{BASE_URL}/cameras/{camera_id}/status")
                if status_response.status_code == 200:
                    status = status_response.json()
                    print(f"   Last Detection: {status.get('last_detection', 'Never')}")
                    print(f"   Recent Detections: {status.get('recent_detections_count', 0)}")
                
                return camera_id
        else:
            print(f"❌ API Error: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return None

def test_video_stream(camera_id):
    """Test if video stream is accessible"""
    print(f"\n🎥 Testing Video Stream for Camera {camera_id}...")
    try:
        response = requests.get(f"{BASE_URL}/cameras/{camera_id}")
        if response.status_code == 200:
            camera = response.json()['camera']
            stream_url = camera['stream_url']
            
            print(f"   Testing stream: {stream_url}")
            
            # Try to open with OpenCV
            cap = cv2.VideoCapture(stream_url)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    print("✅ Video stream is accessible and readable")
                    print(f"   Frame shape: {frame.shape}")
                    cap.release()
                    return True
                else:
                    print("❌ Could not read frame from stream")
            else:
                print("❌ Could not open video stream")
            cap.release()
            return False
    except Exception as e:
        print(f"❌ Stream test error: {e}")
        return False

def check_detection_settings():
    """Check detection configuration"""
    print("\n⚙️ Checking Detection Settings...")
    from config import Config
    
    print(f"   YOLO Model: {Config.YOLO_MODEL}")
    print(f"   Confidence Threshold: {Config.CONFIDENCE_THRESHOLD}")
    print(f"   IoU Threshold: {Config.IOU_THRESHOLD}")
    print(f"   Frame Skip: {Config.FRAME_SKIP}")
    print(f"   Detection Interval: {Config.DETECTION_INTERVAL} seconds")
    print(f"   Detection Images Dir: {Config.DETECTION_IMAGES_DIR}")
    
    # Check if detection directory exists
    if os.path.exists(Config.DETECTION_IMAGES_DIR):
        images = os.listdir(Config.DETECTION_IMAGES_DIR)
        print(f"   Detection Images Found: {len(images)}")
        if images:
            print(f"   Latest images: {images[-3:]}")
    else:
        print("   ❌ Detection images directory doesn't exist")

def force_detection_test(camera_id):
    """Force a detection test"""
    print(f"\n🔬 Force Detection Test for Camera {camera_id}...")
    try:
        # Stop and start camera to trigger fresh detection
        print("   Stopping camera...")
        requests.post(f"{BASE_URL}/cameras/{camera_id}/stop")
        
        import time
        time.sleep(2)
        
        print("   Starting camera...")
        requests.post(f"{BASE_URL}/cameras/{camera_id}/start")
        
        print("   Waiting 10 seconds for detection...")
        time.sleep(10)
        
        # Check for new detections
        response = requests.get(f"{BASE_URL}/cameras/{camera_id}/detections?limit=5")
        if response.status_code == 200:
            detections = response.json()
            print(f"   Total detections: {detections['total']}")
            if detections['detections']:
                latest = detections['detections'][0]
                print(f"   Latest detection: {latest['timestamp']}")
                print(f"   Objects detected: {latest['objects_detected']}")
                return True
        
        print("   ❌ No new detections found")
        return False
        
    except Exception as e:
        print(f"❌ Force detection test error: {e}")
        return False

def suggest_fixes():
    """Suggest potential fixes"""
    print("\n🔧 Potential Fixes:")
    print("1. Lower confidence threshold in config.py:")
    print("   CONFIDENCE_THRESHOLD = 0.3  # Instead of 0.5")
    print()
    print("2. Reduce frame skip for more frequent processing:")
    print("   FRAME_SKIP = 1  # Instead of 5")
    print()
    print("3. Reduce detection interval:")
    print("   DETECTION_INTERVAL = 0.5  # Instead of 2")
    print()
    print("4. Check if your video source has detectable objects")
    print("5. Try using webcam (stream_url: '0') for testing")
    print()
    print("6. Check server logs for error messages")

def main():
    """Main diagnostic function"""
    print("🚀 Detection Diagnostic Tool")
    print("=" * 50)
    
    # Check YOLO model
    yolo_ok = check_yolo_model()
    
    # Check camera status
    camera_id = check_camera_status()
    
    if camera_id:
        # Test video stream
        stream_ok = test_video_stream(camera_id)
        
        # Check detection settings
        check_detection_settings()
        
        # Force detection test
        if stream_ok:
            detection_ok = force_detection_test(camera_id)
            
            if not detection_ok:
                suggest_fixes()
        else:
            print("\n❌ Cannot test detection - video stream not accessible")
            suggest_fixes()
    else:
        print("\n❌ No cameras found - please create a camera first")
    
    print("\n" + "=" * 50)
    print("Diagnostic complete!")

if __name__ == "__main__":
    main()
