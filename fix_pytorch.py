#!/usr/bin/env python3
"""
Fix PyTorch compatibility issues for YOLO model loading
"""

import sys
import os

def fix_pytorch_yolo():
    """Fix PyTorch YOLO loading issues"""
    try:
        import torch
        print(f"PyTorch version: {torch.__version__}")
        
        # Method 1: Add safe globals
        if hasattr(torch.serialization, 'add_safe_globals'):
            try:
                from ultralytics.nn.tasks import DetectionModel
                torch.serialization.add_safe_globals([DetectionModel])
                print("✓ Added DetectionModel to safe globals")
            except Exception as e:
                print(f"Could not add safe globals: {e}")
        
        # Method 2: Test YOLO loading
        print("Testing YOLO model loading...")
        from ultralytics import YOLO
        
        # Try to load a small model
        try:
            model = YOLO('yolov8n.pt')
            print("✓ YOLO model loaded successfully!")
            return True
        except Exception as e:
            print(f"✗ YOLO loading failed: {e}")
            
            # Try with environment variable
            print("Trying with environment variable fix...")
            os.environ['TORCH_SERIALIZATION_SAFE_GLOBALS'] = 'True'
            
            try:
                model = YOLO('yolov8n.pt')
                print("✓ YOLO model loaded with environment variable fix!")
                return True
            except Exception as e2:
                print(f"✗ Still failed: {e2}")
                return False
                
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please install requirements first: pip install -r requirements.txt")
        return False

def main():
    """Main function"""
    print("PyTorch YOLO Compatibility Fixer")
    print("=" * 40)
    
    if fix_pytorch_yolo():
        print("\n✓ PyTorch YOLO compatibility fixed!")
        print("You can now run: python run_server.py")
    else:
        print("\n✗ Could not fix PyTorch YOLO compatibility")
        print("\nTry these manual solutions:")
        print("1. Downgrade PyTorch: pip install torch==2.0.1")
        print("2. Or use older ultralytics: pip install ultralytics==8.0.100")
        print("3. Or run with: TORCH_SERIALIZATION_SAFE_GLOBALS=True python app.py")

if __name__ == "__main__":
    main()
