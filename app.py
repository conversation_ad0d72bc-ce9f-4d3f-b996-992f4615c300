from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import uuid
import os
from datetime import datetime
from config import Config
from database import db, init_db, Camera, Detection
from camera_manager import camera_manager

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = Config.DATABASE_URL
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Enable CORS
CORS(app)

# Initialize database
init_db(app)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/cameras', methods=['POST'])
def create_camera():
    """Create a new camera"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'stream_type', 'stream_url']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate stream type
        if data['stream_type'] not in Config.SUPPORTED_STREAM_TYPES:
            return jsonify({
                'error': f'Unsupported stream type. Supported types: {Config.SUPPORTED_STREAM_TYPES}'
            }), 400
        
        # Generate camera ID if not provided
        camera_id = data.get('camera_id', str(uuid.uuid4()))
        
        # Check if camera ID already exists
        existing_camera = Camera.query.get(camera_id)
        if existing_camera:
            return jsonify({'error': 'Camera ID already exists'}), 409
        
        # Get object types for detection (use default if not provided)
        object_types = data.get('object_types_for_detection', Config.DEFAULT_OBJECT_TYPES)
        
        # Create new camera
        camera = Camera(
            id=camera_id,
            name=data['name'],
            stream_type=data['stream_type'],
            stream_url=data['stream_url']
        )
        camera.set_object_types(object_types)
        
        # Save to database
        db.session.add(camera)
        db.session.commit()
        
        # Start camera stream processing
        camera_manager.start_camera_stream(camera)
        
        return jsonify({
            'message': 'Camera created successfully',
            'camera': camera.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/cameras', methods=['GET'])
def list_cameras():
    """List all cameras"""
    try:
        cameras = Camera.query.all()
        cameras_data = []
        
        for camera in cameras:
            camera_dict = camera.to_dict()
            # Add status information
            status = camera_manager.get_camera_status(camera.id)
            camera_dict.update(status)
            cameras_data.append(camera_dict)
        
        return jsonify({
            'cameras': cameras_data,
            'total': len(cameras_data)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>', methods=['GET'])
def get_camera(camera_id):
    """Get specific camera details"""
    try:
        camera = Camera.query.get(camera_id)
        if not camera:
            return jsonify({'error': 'Camera not found'}), 404
        
        camera_dict = camera.to_dict()
        status = camera_manager.get_camera_status(camera_id)
        camera_dict.update(status)
        
        return jsonify({'camera': camera_dict})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>', methods=['DELETE'])
def delete_camera(camera_id):
    """Delete a camera"""
    try:
        camera = Camera.query.get(camera_id)
        if not camera:
            return jsonify({'error': 'Camera not found'}), 404
        
        # Stop camera stream
        camera_manager.stop_camera_stream(camera_id)
        
        # Delete camera (this will cascade delete detections)
        db.session.delete(camera)
        db.session.commit()
        
        return jsonify({'message': 'Camera deleted successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>/detections', methods=['GET'])
def get_camera_detections(camera_id):
    """Get detection results for a specific camera"""
    try:
        camera = Camera.query.get(camera_id)
        if not camera:
            return jsonify({'error': 'Camera not found'}), 404
        
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Get detections with pagination
        detections_query = Detection.query.filter_by(camera_id=camera_id)\
                                         .order_by(Detection.timestamp.desc())\
                                         .offset(offset)\
                                         .limit(limit)
        
        detections = detections_query.all()
        detections_data = [detection.to_dict() for detection in detections]
        
        # Get total count
        total_count = Detection.query.filter_by(camera_id=camera_id).count()
        
        return jsonify({
            'camera_id': camera_id,
            'detections': detections_data,
            'total': total_count,
            'limit': limit,
            'offset': offset
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>/detections/<int:detection_id>/image', methods=['GET'])
def get_detection_image(camera_id, detection_id):
    """Get detection image"""
    try:
        detection = Detection.query.filter_by(id=detection_id, camera_id=camera_id).first()
        if not detection:
            return jsonify({'error': 'Detection not found'}), 404
        
        if not os.path.exists(detection.image_path):
            return jsonify({'error': 'Image file not found'}), 404
        
        return send_file(detection.image_path, mimetype='image/jpeg')
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>/status', methods=['GET'])
def get_camera_status(camera_id):
    """Get camera status"""
    try:
        camera = Camera.query.get(camera_id)
        if not camera:
            return jsonify({'error': 'Camera not found'}), 404
        
        status = camera_manager.get_camera_status(camera_id)
        
        # Add additional status information
        recent_detections = Detection.query.filter_by(camera_id=camera_id)\
                                          .order_by(Detection.timestamp.desc())\
                                          .limit(5).all()
        
        status.update({
            'camera_name': camera.name,
            'stream_url': camera.stream_url,
            'last_detection': camera.last_detection.isoformat() if camera.last_detection else None,
            'recent_detections_count': len(recent_detections),
            'total_detections': Detection.query.filter_by(camera_id=camera_id).count()
        })
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>/start', methods=['POST'])
def start_camera(camera_id):
    """Start camera stream processing"""
    try:
        camera = Camera.query.get(camera_id)
        if not camera:
            return jsonify({'error': 'Camera not found'}), 404
        
        if camera_manager.start_camera_stream(camera):
            return jsonify({'message': 'Camera stream started successfully'})
        else:
            return jsonify({'message': 'Camera stream is already running'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/cameras/<camera_id>/stop', methods=['POST'])
def stop_camera(camera_id):
    """Stop camera stream processing"""
    try:
        camera = Camera.query.get(camera_id)
        if not camera:
            return jsonify({'error': 'Camera not found'}), 404
        
        if camera_manager.stop_camera_stream(camera_id):
            return jsonify({'message': 'Camera stream stopped successfully'})
        else:
            return jsonify({'message': 'Camera stream was not running'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host=Config.HOST, port=Config.PORT, debug=Config.DEBUG)
