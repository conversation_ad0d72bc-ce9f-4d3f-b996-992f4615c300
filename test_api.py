#!/usr/bin/env python3
"""
Test script for the IP Camera Object Detection REST API
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_health_check():
    """Test health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_create_camera():
    """Test creating a camera"""
    print("Testing camera creation...")
    
    camera_data = {
        "camera_id": "test_cam_001",
        "name": "Test Camera 1",
        "stream_type": "file",
        "stream_url": "0",  # Use webcam (device 0) for testing
        "object_types_for_detection": ["person", "car", "bicycle", "dog", "cat"]
    }
    
    response = requests.post(
        f"{BASE_URL}/cameras",
        headers={"Content-Type": "application/json"},
        json=camera_data
    )
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)
    
    return response.status_code == 201

def test_list_cameras():
    """Test listing cameras"""
    print("Testing camera listing...")
    
    response = requests.get(f"{BASE_URL}/cameras")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_get_camera_status(camera_id):
    """Test getting camera status"""
    print(f"Testing camera status for {camera_id}...")
    
    response = requests.get(f"{BASE_URL}/cameras/{camera_id}/status")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_get_detections(camera_id):
    """Test getting camera detections"""
    print(f"Testing detections for {camera_id}...")
    
    response = requests.get(f"{BASE_URL}/cameras/{camera_id}/detections?limit=5")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_stop_camera(camera_id):
    """Test stopping camera"""
    print(f"Testing stop camera {camera_id}...")
    
    response = requests.post(f"{BASE_URL}/cameras/{camera_id}/stop")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_delete_camera(camera_id):
    """Test deleting camera"""
    print(f"Testing delete camera {camera_id}...")
    
    response = requests.delete(f"{BASE_URL}/cameras/{camera_id}")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def main():
    """Run all tests"""
    print("Starting API tests...")
    print("=" * 50)
    
    try:
        # Test health check
        test_health_check()
        
        # Test camera creation
        camera_created = test_create_camera()
        
        if camera_created:
            camera_id = "test_cam_001"
            
            # Wait a moment for camera to start
            print("Waiting 5 seconds for camera to start processing...")
            time.sleep(5)
            
            # Test other endpoints
            test_list_cameras()
            test_get_camera_status(camera_id)
            
            # Wait a bit more for potential detections
            print("Waiting 10 seconds for potential detections...")
            time.sleep(10)
            
            test_get_detections(camera_id)
            test_stop_camera(camera_id)
            test_delete_camera(camera_id)
        
        print("All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the API server.")
        print("Make sure the server is running with: python app.py")
    except Exception as e:
        print(f"Error during testing: {e}")

if __name__ == "__main__":
    main()
