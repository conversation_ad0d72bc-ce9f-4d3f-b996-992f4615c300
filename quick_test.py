#!/usr/bin/env python3
"""
Quick test script for detection issues
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:8080"

def create_test_camera():
    """Create a test camera using webcam"""
    print("Creating test camera...")
    
    camera_data = {
        "camera_id": "webcam_test",
        "name": "Webcam Test Camera",
        "stream_type": "file",
        "stream_url": "0",  # Use default webcam
        "object_types_for_detection": ["person", "car", "bicycle", "dog", "cat", "bottle", "chair", "laptop"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/cameras",
            headers={"Content-Type": "application/json"},
            json=camera_data
        )
        
        if response.status_code == 201:
            print("✅ Test camera created successfully")
            return "webcam_test"
        else:
            print(f"❌ Failed to create camera: {response.json()}")
            return None
    except Exception as e:
        print(f"❌ Error creating camera: {e}")
        return None

def check_camera_status(camera_id):
    """Check camera status"""
    print(f"Checking status for camera {camera_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/cameras/{camera_id}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Camera Status:")
            print(f"   Active: {status.get('is_active')}")
            print(f"   Streaming: {status.get('is_streaming')}")
            print(f"   Total Detections: {status.get('total_detections', 0)}")
            print(f"   Last Detection: {status.get('last_detection', 'Never')}")
            return True
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return False

def wait_for_detections(camera_id, wait_time=30):
    """Wait for detections to appear"""
    print(f"Waiting {wait_time} seconds for detections...")
    
    for i in range(wait_time):
        time.sleep(1)
        if i % 5 == 0:  # Check every 5 seconds
            try:
                response = requests.get(f"{BASE_URL}/cameras/{camera_id}/detections?limit=1")
                if response.status_code == 200:
                    detections = response.json()
                    if detections['total'] > 0:
                        print(f"✅ Detection found! Total: {detections['total']}")
                        latest = detections['detections'][0]
                        print(f"   Objects: {latest['objects_detected']}")
                        print(f"   Confidence: {latest['confidence_scores']}")
                        print(f"   Timestamp: {latest['timestamp']}")
                        return True
                    else:
                        print(f"   Still waiting... ({i+1}/{wait_time}s)")
            except:
                pass
    
    print("❌ No detections found after waiting")
    return False

def cleanup_test_camera(camera_id):
    """Remove test camera"""
    print(f"Cleaning up test camera {camera_id}...")
    try:
        response = requests.delete(f"{BASE_URL}/cameras/{camera_id}")
        if response.status_code == 200:
            print("✅ Test camera removed")
        else:
            print(f"❌ Failed to remove camera: {response.status_code}")
    except Exception as e:
        print(f"❌ Error removing camera: {e}")

def main():
    """Main test function"""
    print("🚀 Quick Detection Test")
    print("=" * 40)
    
    # Create test camera
    camera_id = create_test_camera()
    if not camera_id:
        print("Cannot proceed without camera")
        return
    
    # Wait a moment for camera to start
    print("Waiting 5 seconds for camera to initialize...")
    time.sleep(5)
    
    # Check status
    if not check_camera_status(camera_id):
        cleanup_test_camera(camera_id)
        return
    
    # Wait for detections
    detection_found = wait_for_detections(camera_id, 30)
    
    if not detection_found:
        print("\n🔧 Troubleshooting Tips:")
        print("1. Make sure your webcam is working")
        print("2. Try moving in front of the camera")
        print("3. Check server logs for errors")
        print("4. Run: python debug_detection.py")
    
    # Cleanup
    cleanup_test_camera(camera_id)
    
    print("\n" + "=" * 40)
    print("Test complete!")

if __name__ == "__main__":
    main()
