{"create_camera_examples": [{"description": "Create camera with RTSP stream for person and vehicle detection", "method": "POST", "url": "http://localhost:5000/cameras", "headers": {"Content-Type": "application/json"}, "body": {"camera_id": "cam_001", "name": "Front Door Camera", "stream_type": "rtsp", "stream_url": "rtsp://*************:554/stream1", "object_types_for_detection": ["person", "car", "bicycle", "motorcycle", "truck", "bus"]}}, {"description": "Create camera with HTTP stream for general object detection", "method": "POST", "url": "http://localhost:5000/cameras", "headers": {"Content-Type": "application/json"}, "body": {"name": "Parking Lot Camera", "stream_type": "http", "stream_url": "http://*************:8080/video", "object_types_for_detection": ["person", "car", "bicycle", "motorcycle", "truck", "bus", "dog", "cat"]}}, {"description": "Create camera with file input for testing", "method": "POST", "url": "http://localhost:5000/cameras", "headers": {"Content-Type": "application/json"}, "body": {"camera_id": "test_cam", "name": "Test Video File", "stream_type": "file", "stream_url": "/path/to/test_video.mp4", "object_types_for_detection": ["person", "car", "bicycle"]}}, {"description": "Create camera with default object detection (all COCO classes)", "method": "POST", "url": "http://localhost:5000/cameras", "headers": {"Content-Type": "application/json"}, "body": {"name": "Security Camera 1", "stream_type": "rtsp", "stream_url": "rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0"}}], "list_cameras_examples": [{"description": "Get all cameras", "method": "GET", "url": "http://localhost:5000/cameras", "expected_response": {"cameras": [{"camera_id": "cam_001", "name": "Front Door Camera", "stream_type": "rtsp", "stream_url": "rtsp://*************:554/stream1", "object_types": ["person", "car", "bicycle", "motorcycle", "truck", "bus"], "is_active": true, "created_at": "2024-01-15T10:30:00", "last_detection": "2024-01-15T12:45:30", "total_detections": 25, "is_streaming": true}], "total": 1}}], "get_camera_detections_examples": [{"description": "Get recent detections for a specific camera", "method": "GET", "url": "http://localhost:5000/cameras/cam_001/detections", "expected_response": {"camera_id": "cam_001", "detections": [{"id": 123, "camera_id": "cam_001", "timestamp": "2024-01-15T12:45:30", "image_path": "detection_images/cam_001_20240115_124530.jpg", "objects_detected": ["person", "car"], "confidence_scores": [0.85, 0.92], "bounding_boxes": [[100, 150, 200, 300], [300, 100, 500, 250]]}], "total": 25, "limit": 50, "offset": 0}}, {"description": "Get detections with pagination", "method": "GET", "url": "http://localhost:5000/cameras/cam_001/detections?limit=10&offset=20", "query_parameters": {"limit": "Number of results to return (default: 50)", "offset": "Number of results to skip (default: 0)"}}], "camera_management_examples": [{"description": "Get specific camera details", "method": "GET", "url": "http://localhost:5000/cameras/cam_001"}, {"description": "Get camera status", "method": "GET", "url": "http://localhost:5000/cameras/cam_001/status"}, {"description": "Start camera stream processing", "method": "POST", "url": "http://localhost:5000/cameras/cam_001/start"}, {"description": "Stop camera stream processing", "method": "POST", "url": "http://localhost:5000/cameras/cam_001/stop"}, {"description": "Delete camera", "method": "DELETE", "url": "http://localhost:5000/cameras/cam_001"}, {"description": "Get detection image", "method": "GET", "url": "http://localhost:5000/cameras/cam_001/detections/123/image", "response_type": "image/jpeg"}], "curl_examples": [{"description": "Create camera using curl", "command": "curl -X POST http://localhost:5000/cameras \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"camera_id\": \"cam_001\",\n    \"name\": \"Front Door Camera\",\n    \"stream_type\": \"rtsp\",\n    \"stream_url\": \"rtsp://*************:554/stream1\",\n    \"object_types_for_detection\": [\"person\", \"car\", \"bicycle\"]\n  }'"}, {"description": "List all cameras using curl", "command": "curl -X GET http://localhost:5000/cameras"}, {"description": "Get camera detections using curl", "command": "curl -X GET \"http://localhost:5000/cameras/cam_001/detections?limit=10\""}, {"description": "Get camera status using curl", "command": "curl -X GET http://localhost:5000/cameras/cam_001/status"}], "common_rtsp_urls": [{"brand": "Generic RTSP", "url_format": "rtsp://username:password@ip_address:port/path"}, {"brand": "Hikvision", "url_format": "rtsp://username:password@ip_address:554/Streaming/Channels/101"}, {"brand": "Dahua", "url_format": "rtsp://username:password@ip_address:554/cam/realmonitor?channel=1&subtype=0"}, {"brand": "Axis", "url_format": "rtsp://username:password@ip_address:554/axis-media/media.amp"}, {"brand": "Foscam", "url_format": "rtsp://username:password@ip_address:554/videoMain"}]}