# IP Camera Object Detection REST Service

A Python REST service that processes IP camera streams for real-time object detection using YOLO (You Only Look Once) deep learning model.

## Features

- **Real-time Object Detection**: Uses YOLOv8 for fast and accurate object detection
- **Multiple Stream Support**: Supports RTSP, HTTP, HTTPS, and file inputs
- **REST API**: Complete REST API for camera management and detection results
- **Database Storage**: SQLite database for storing camera configurations and detection results
- **Image Storage**: Saves detection images with bounding boxes and labels
- **Multi-threading**: Processes multiple camera streams simultaneously
- **Configurable Detection**: Customize which object types to detect per camera

## Requirements

- Python 3.8+
- OpenCV
- YOLOv8 (ultralytics)
- Flask
- SQLAlchemy

## Installation

1. **Clone or create the project directory**:
   ```bash
   mkdir mycam && cd mycam
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python app.py
   ```

The service will start on `http://localhost:5000`

## API Endpoints

### Camera Management

#### Create Camera
```http
POST /cameras
Content-Type: application/json

{
  "camera_id": "cam_001",
  "name": "Front Door Camera",
  "stream_type": "rtsp",
  "stream_url": "rtsp://*************:554/stream1",
  "object_types_for_detection": ["person", "car", "bicycle"]
}
```

#### List All Cameras
```http
GET /cameras
```

#### Get Camera Details
```http
GET /cameras/{camera_id}
```

#### Delete Camera
```http
DELETE /cameras/{camera_id}
```

### Detection Results

#### Get Camera Detections
```http
GET /cameras/{camera_id}/detections?limit=50&offset=0
```

#### Get Detection Image
```http
GET /cameras/{camera_id}/detections/{detection_id}/image
```

### Camera Control

#### Get Camera Status
```http
GET /cameras/{camera_id}/status
```

#### Start Camera Stream
```http
POST /cameras/{camera_id}/start
```

#### Stop Camera Stream
```http
POST /cameras/{camera_id}/stop
```

## Configuration

Edit `config.py` to customize:

- **YOLO Model**: Choose between yolov8n.pt (fastest) to yolov8x.pt (most accurate)
- **Detection Thresholds**: Confidence and IoU thresholds
- **Processing Settings**: Frame skip rate, detection interval
- **Storage Settings**: Image storage directory, max images per camera

## Supported Object Types

The service can detect 80+ object types from the COCO dataset including:
- People and animals: person, dog, cat, horse, etc.
- Vehicles: car, bicycle, motorcycle, bus, truck, etc.
- Objects: bottle, chair, laptop, cell phone, etc.

## Example Usage

### 1. Create a Camera
```bash
curl -X POST http://localhost:5000/cameras \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Security Camera 1",
    "stream_type": "rtsp",
    "stream_url": "rtsp://admin:password@*************:554/stream1",
    "object_types_for_detection": ["person", "car", "bicycle"]
  }'
```

### 2. List Cameras
```bash
curl http://localhost:5000/cameras
```

### 3. Get Detection Results
```bash
curl "http://localhost:5000/cameras/cam_001/detections?limit=10"
```

## Common RTSP URL Formats

- **Generic**: `rtsp://username:password@ip_address:port/path`
- **Hikvision**: `rtsp://admin:password@*************:554/Streaming/Channels/101`
- **Dahua**: `rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0`
- **Axis**: `rtsp://user:pass@*************:554/axis-media/media.amp`

## File Structure

```
mycam/
├── app.py                 # Main Flask application
├── camera_manager.py      # Camera stream processing and YOLO detection
├── database.py           # Database models and operations
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── sample_requests.json  # Sample API requests
├── README.md            # This file
├── cameras.db           # SQLite database (created automatically)
└── detection_images/    # Stored detection images (created automatically)
```

## Troubleshooting

1. **YOLO Model Download**: The first run will download the YOLO model (~6MB for yolov8n.pt)
2. **Camera Connection**: Ensure camera URLs are accessible and credentials are correct
3. **Performance**: Adjust `FRAME_SKIP` and `DETECTION_INTERVAL` in config.py for better performance
4. **Memory Usage**: Set `MAX_IMAGES_PER_CAMERA` to limit stored images

## Health Check

```bash
curl http://localhost:5000/health
```

## License

This project is open source and available under the MIT License.
