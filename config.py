import os

class Config:
    # Database configuration
    DATABASE_URL = 'sqlite:///cameras.db'
    
    # YOLO model configuration
    YOLO_MODEL = 'yolov8n.pt'  # You can use yolov8s.pt, yolov8m.pt, yolov8l.pt, yolov8x.pt for better accuracy
    
    # Detection settings
    CONFIDENCE_THRESHOLD = 0.5
    IOU_THRESHOLD = 0.45
    
    # Image storage
    DETECTION_IMAGES_DIR = 'detection_images'
    MAX_IMAGES_PER_CAMERA = 100
    
    # Stream processing
    FRAME_SKIP = 5  # Process every 5th frame to reduce CPU usage
    DETECTION_INTERVAL = 2  # Seconds between detections
    
    # API settings
    HOST = '0.0.0.0'
    PORT = 5000
    DEBUG = True
    
    # Supported stream types
    SUPPORTED_STREAM_TYPES = ['rtsp', 'http', 'https', 'file']
    
    # Default object types for detection (COCO classes)
    DEFAULT_OBJECT_TYPES = [
        'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
        'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
        'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
        'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
        'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
        'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
        'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
        'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
        'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
        'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
        'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
        'toothbrush'
    ]

# Create directories if they don't exist
os.makedirs(Config.DETECTION_IMAGES_DIR, exist_ok=True)
